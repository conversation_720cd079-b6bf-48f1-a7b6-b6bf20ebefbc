<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>بوابة الأخبار الحكومية - جمهورية العراق</title>

    <!-- Bootstrap 5.3 RTL CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css"
      rel="stylesheet"
    />

    <!-- Font Awesome Icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <!-- Google Fonts - Arabic -->
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      /* ===== متغيرات الألوان الأساسية ===== */
      :root {
        --primary-color: #1e3a8a;
        --secondary-color: #3b82f6;
        --accent-color: #f59e0b;
        --text-dark: #1f2937;
        --text-light: #6b7280;
        --bg-light: #f8fafc;
        --border-color: #e5e7eb;
      }

      /* ===== الوضع الداكن ===== */
      [data-theme='dark'] {
        --primary-color: #3b82f6;
        --secondary-color: #1e40af;
        --accent-color: #fbbf24;
        --text-dark: #f9fafb;
        --text-light: #d1d5db;
        --bg-light: #111827;
        --border-color: #374151;
      }

      /* ===== الخطوط والتنسيق العام ===== */
      * {
        font-family: 'Cairo', sans-serif;
      }

      body {
        background-color: var(--bg-light);
        color: var(--text-dark);
        transition: all 0.3s ease;
      }

      /* ===== الهيدر العلوي ===== */
      .top-header {
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        color: white;
        padding: 15px 0;
      }

      .logo-section {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .logo-img {
        width: 60px;
        height: 60px;
        background: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-color);
        font-size: 24px;
        font-weight: bold;
      }

      .site-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin: 0;
      }

      .site-subtitle {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
      }

      /* ===== شريط التنقل المحسن ===== */
      .main-navbar {
        background: white;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border-bottom: 3px solid var(--accent-color);
        padding: 0;
      }

      .navbar-nav {
        gap: 5px;
      }

      .navbar-nav .nav-link {
        color: var(--text-dark);
        font-weight: 600;
        padding: 18px 25px;
        transition: all 0.3s ease;
        border-radius: 12px;
        margin: 8px 0;
        position: relative;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .navbar-nav .nav-link::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 3px;
        background: var(--accent-color);
        transition: all 0.3s ease;
        transform: translateX(-50%);
      }

      .navbar-nav .nav-link:hover,
      .navbar-nav .nav-link.active {
        color: var(--primary-color);
        background-color: var(--bg-light);
        transform: translateY(-2px);
      }

      .navbar-nav .nav-link:hover::before,
      .navbar-nav .nav-link.active::before {
        width: 80%;
      }

      .navbar-nav .nav-link i {
        font-size: 1.1rem;
        transition: transform 0.3s ease;
      }

      .navbar-nav .nav-link:hover i {
        transform: scale(1.1);
      }

      /* ===== القوائم المنسدلة المحسنة ===== */
      .dropdown-menu {
        border: none;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border-radius: 15px;
        padding: 15px 0;
        margin-top: 10px;
        background: white;
        border: 1px solid var(--border-color);
      }

      .dropdown-item {
        padding: 12px 25px;
        font-weight: 500;
        color: var(--text-dark);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 2px 10px;
        border-radius: 10px;
      }

      .dropdown-item:hover {
        background: var(--primary-color);
        color: white;
        transform: translateX(5px);
      }

      .dropdown-item i {
        font-size: 1rem;
        width: 20px;
        text-align: center;
        transition: transform 0.3s ease;
      }

      .dropdown-item:hover i {
        transform: scale(1.1);
        color: var(--accent-color);
      }

      .dropdown-divider {
        margin: 10px 20px;
        border-color: var(--border-color);
      }

      /* ===== زر الوضع الداكن المحسن ===== */
      .theme-toggle {
        background: linear-gradient(135deg, var(--accent-color), #f97316);
        border: none;
        color: white;
        padding: 12px 20px;
        border-radius: 30px;
        transition: all 0.3s ease;
        font-weight: 600;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        position: relative;
        overflow: hidden;
      }

      .theme-toggle::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s ease;
      }

      .theme-toggle:hover {
        background: linear-gradient(135deg, #d97706, #ea580c);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
      }

      .theme-toggle:hover::before {
        left: 100%;
      }

      .theme-toggle i {
        font-size: 1.1rem;
        transition: transform 0.3s ease;
      }

      .theme-toggle:hover i {
        transform: rotate(180deg);
      }

      /* ===== تحسين البحث ===== */
      .navbar .d-flex {
        gap: 10px;
      }

      .form-control {
        border: 2px solid var(--border-color);
        border-radius: 25px;
        padding: 12px 20px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: var(--bg-light);
      }

      .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.1);
        background: white;
        transform: scale(1.02);
      }

      .btn-outline-primary {
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
        border-radius: 25px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .btn-outline-primary:hover {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
      }

      /* ===== السلايدر الرئيسي ===== */
      .hero-slider {
        margin: 30px 0;
      }

      .carousel-item {
        height: 400px;
        position: relative;
      }

      .carousel-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .carousel-caption {
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        bottom: 0;
        right: 0;
        left: 0;
        padding: 40px 20px 20px;
      }

      .carousel-caption h5 {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 10px;
      }

      /* ===== العناوين الرئيسية ===== */
      .section-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 30px;
        padding: 20px 0;
        border-bottom: 3px solid var(--accent-color);
        position: relative;
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .section-title::after {
        content: '';
        flex: 1;
        height: 1px;
        background: linear-gradient(to left, var(--border-color), transparent);
      }

      .section-title i {
        font-size: 1.8rem;
        color: var(--accent-color);
        background: var(--bg-light);
        padding: 10px;
        border-radius: 50%;
        border: 2px solid var(--accent-color);
      }

      /* ===== بطاقات الأخبار المحسنة ===== */
      .news-card {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        margin-bottom: 30px;
        border: 1px solid var(--border-color);
        position: relative;
      }

      .news-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(
          90deg,
          var(--primary-color),
          var(--accent-color)
        );
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .news-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
      }

      .news-card:hover::before {
        opacity: 1;
      }

      .news-card img {
        width: 100%;
        height: 220px;
        object-fit: cover;
        transition: transform 0.4s ease;
      }

      .news-card:hover img {
        transform: scale(1.05);
      }

      .news-card-body {
        padding: 25px;
        position: relative;
      }

      .news-category {
        background: linear-gradient(135deg, var(--accent-color), #f97316);
        color: white;
        padding: 8px 18px;
        border-radius: 25px;
        font-size: 0.75rem;
        font-weight: 700;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 15px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
      }

      .news-category::before {
        content: '';
        width: 6px;
        height: 6px;
        background: white;
        border-radius: 50%;
      }

      .news-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-dark);
        margin-bottom: 12px;
        line-height: 1.4;
        transition: color 0.3s ease;
      }

      .news-title a {
        color: inherit;
        text-decoration: none;
        transition: color 0.3s ease;
      }

      .news-title a:hover {
        color: var(--primary-color);
      }

      .news-excerpt {
        color: var(--text-light);
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 20px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .news-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.85rem;
        color: var(--text-light);
        padding-top: 15px;
        border-top: 1px solid var(--border-color);
      }

      .news-meta span {
        display: flex;
        align-items: center;
        gap: 6px;
        font-weight: 500;
      }

      .news-meta i {
        color: var(--accent-color);
        font-size: 0.9rem;
      }

      /* ===== الشريط الجانبي المحسن ===== */
      .sidebar {
        background: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        margin-bottom: 30px;
        border: 1px solid var(--border-color);
        position: relative;
        overflow: hidden;
      }

      .sidebar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
      }

      .sidebar-title {
        color: var(--primary-color);
        font-weight: 700;
        font-size: 1.3rem;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--accent-color);
        position: relative;
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .sidebar-title i {
        font-size: 1.4rem;
        color: var(--accent-color);
        background: var(--bg-light);
        padding: 8px;
        border-radius: 50%;
        border: 2px solid var(--accent-color);
      }

      .trending-item {
        display: flex;
        gap: 15px;
        padding: 18px 0;
        border-bottom: 1px solid var(--border-color);
        transition: all 0.3s ease;
        border-radius: 10px;
        margin-bottom: 5px;
      }

      .trending-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .trending-item:hover {
        background: var(--bg-light);
        padding-left: 15px;
        padding-right: 15px;
        margin-left: -15px;
        margin-right: -15px;
      }
      }

      .trending-number {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1rem;
        box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
        flex-shrink: 0;
        position: relative;
      }

      .trending-number::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(135deg, var(--accent-color), #f97316);
        border-radius: 50%;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .trending-item:hover .trending-number::after {
        opacity: 1;
      }

      .trending-content {
        flex: 1;
        min-width: 0;
      }

      .trending-content h6 {
        font-size: 0.95rem;
        font-weight: 600;
        margin-bottom: 8px;
        line-height: 1.4;
        color: var(--text-dark);
        transition: color 0.3s ease;
      }

      .trending-content h6 a {
        color: inherit;
        text-decoration: none;
        transition: color 0.3s ease;
      }

      .trending-content h6 a:hover {
        color: var(--primary-color);
      }

      .trending-content small {
        color: var(--text-light);
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 5px;
        font-weight: 500;
      }

      .trending-content small i {
        color: var(--accent-color);
        font-size: 0.75rem;
      }

      /* ===== تحسين القوائم ===== */
      .list-group-item {
        border: none;
        padding: 15px 20px;
        margin-bottom: 8px;
        border-radius: 12px;
        transition: all 0.3s ease;
        background: var(--bg-light);
        border-left: 4px solid transparent;
      }

      .list-group-item:hover {
        background: var(--primary-color);
        color: white;
        border-left-color: var(--accent-color);
        transform: translateX(5px);
      }

      .list-group-item:hover i {
        color: var(--accent-color);
        transform: scale(1.1);
      }

      .list-group-item i {
        transition: all 0.3s ease;
        font-size: 1rem;
      }

      /* ===== الفوتر ===== */
      .main-footer {
        background: var(--primary-color);
        color: white;
        padding: 40px 0 20px;
        margin-top: 50px;
      }

      .footer-section h5 {
        color: var(--accent-color);
        margin-bottom: 20px;
        font-weight: 700;
      }

      .footer-links {
        list-style: none;
        padding: 0;
      }

      .footer-links li {
        margin-bottom: 10px;
      }

      .footer-links a {
        color: white;
        text-decoration: none;
        transition: color 0.3s ease;
      }

      .footer-links a:hover {
        color: var(--accent-color);
      }

      .footer-bottom {
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        margin-top: 30px;
        padding-top: 20px;
        text-align: center;
      }

      /* ===== الاستجابة للشاشات الصغيرة المحسنة ===== */
      @media (max-width: 768px) {
        .site-title {
          font-size: 1.3rem;
        }

        .site-subtitle {
          font-size: 0.8rem;
        }

        .carousel-item {
          height: 280px;
        }

        .carousel-caption {
          padding: 20px 15px 15px;
        }

        .carousel-caption h5 {
          font-size: 1.1rem;
          margin-bottom: 8px;
        }

        .carousel-caption p {
          font-size: 0.85rem;
          margin-bottom: 10px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .logo-section {
          flex-direction: column;
          text-align: center;
          gap: 10px;
        }

        .logo-img {
          width: 50px;
          height: 50px;
          font-size: 20px;
        }

        .section-title {
          font-size: 1.5rem;
          padding: 15px 0;
          flex-direction: column;
          text-align: center;
          gap: 10px;
        }

        .section-title::after {
          display: none;
        }

        .news-card {
          margin-bottom: 20px;
        }

        .news-card-body {
          padding: 20px;
        }

        .news-title {
          font-size: 1.1rem;
        }

        .sidebar {
          padding: 20px;
          margin-bottom: 20px;
        }

        .sidebar-title {
          font-size: 1.1rem;
          margin-bottom: 15px;
        }

        .trending-item {
          padding: 12px 0;
        }

        .trending-number {
          width: 35px;
          height: 35px;
          font-size: 0.9rem;
        }

        .navbar-nav .nav-link {
          padding: 12px 15px;
          margin: 4px 0;
        }

        .theme-toggle {
          padding: 10px 15px;
          font-size: 0.85rem;
        }

        .form-control {
          padding: 10px 15px;
          font-size: 0.85rem;
        }

        .btn-outline-primary {
          padding: 10px 15px;
        }
      }

      @media (max-width: 576px) {
        .top-header {
          padding: 10px 0;
        }

        .site-title {
          font-size: 1.1rem;
        }

        .carousel-item {
          height: 220px;
        }

        .carousel-caption h5 {
          font-size: 1rem;
        }

        .news-card-body {
          padding: 15px;
        }

        .news-title {
          font-size: 1rem;
        }

        .news-excerpt {
          font-size: 0.85rem;
          -webkit-line-clamp: 2;
        }

        .sidebar {
          padding: 15px;
        }

        .trending-content h6 {
          font-size: 0.85rem;
        }

        .footer-section {
          margin-bottom: 20px;
        }
      }

      /* ===== تأثيرات الوضع الداكن المحسنة ===== */
      [data-theme='dark'] body {
        background: linear-gradient(135deg, #0f172a, #1e293b);
        background-attachment: fixed;
      }

      [data-theme='dark'] .main-navbar,
      [data-theme='dark'] .news-card,
      [data-theme='dark'] .sidebar {
        background: rgba(30, 41, 59, 0.8);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(59, 130, 246, 0.1);
        color: var(--text-dark);
      }

      [data-theme='dark'] .navbar-nav .nav-link {
        color: var(--text-dark);
      }

      [data-theme='dark'] .navbar-nav .nav-link:hover,
      [data-theme='dark'] .navbar-nav .nav-link.active {
        background: rgba(59, 130, 246, 0.1);
        color: var(--accent-color);
      }

      [data-theme='dark'] .dropdown-menu {
        background: rgba(30, 41, 59, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(59, 130, 246, 0.2);
      }

      [data-theme='dark'] .dropdown-item {
        color: var(--text-dark);
      }

      [data-theme='dark'] .dropdown-item:hover {
        background: rgba(59, 130, 246, 0.2);
        color: var(--accent-color);
      }

      [data-theme='dark'] .form-control {
        background: rgba(30, 41, 59, 0.5);
        border-color: rgba(59, 130, 246, 0.3);
        color: var(--text-dark);
      }

      [data-theme='dark'] .form-control:focus {
        background: rgba(30, 41, 59, 0.8);
        border-color: var(--accent-color);
        color: var(--text-dark);
      }

      [data-theme='dark'] .list-group-item {
        background: rgba(30, 41, 59, 0.3);
        color: var(--text-dark);
        border-color: rgba(59, 130, 246, 0.1);
      }

      [data-theme='dark'] .list-group-item:hover {
        background: rgba(59, 130, 246, 0.2);
        color: var(--accent-color);
      }

      [data-theme='dark'] .carousel-caption {
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
      }

      [data-theme='dark'] .footer-bottom {
        border-top-color: rgba(59, 130, 246, 0.2);
      }
    </style>
  </head>
  <body>
    <!-- ===== الهيدر العلوي ===== -->
    <header class="top-header">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-md-8">
            <div class="logo-section">
              <div class="logo-img">
                <i class="fas fa-flag"></i>
              </div>
              <div>
                <h1 class="site-title">بوابة الأخبار الحكومية</h1>
                <p class="site-subtitle">جمهورية العراق - وزارة الإعلام</p>
              </div>
            </div>
          </div>
          <div class="col-md-4 text-end">
            <button class="theme-toggle" id="themeToggle">
              <i class="fas fa-moon" id="themeIcon"></i>
              <span id="themeText">الوضع الداكن</span>
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- ===== شريط التنقل الرئيسي ===== -->
    <nav class="navbar navbar-expand-lg main-navbar sticky-top">
      <div class="container">
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#mainNavbar"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="mainNavbar">
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <a class="nav-link active" href="#"
                ><i class="fas fa-home ms-2"></i>الرئيسية</a
              >
            </li>
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                data-bs-toggle="dropdown"
              >
                <i class="fas fa-newspaper ms-2"></i>الأقسام
              </a>
              <ul class="dropdown-menu">
                <li>
                  <a class="dropdown-item" href="#"
                    ><i class="fas fa-landmark ms-2"></i>سياسة</a
                  >
                </li>
                <li>
                  <a class="dropdown-item" href="#"
                    ><i class="fas fa-chart-line ms-2"></i>اقتصاد</a
                  >
                </li>
                <li>
                  <a class="dropdown-item" href="#"
                    ><i class="fas fa-futbol ms-2"></i>رياضة</a
                  >
                </li>
                <li>
                  <a class="dropdown-item" href="#"
                    ><i class="fas fa-palette ms-2"></i>ثقافة</a
                  >
                </li>
                <li>
                  <a class="dropdown-item" href="#"
                    ><i class="fas fa-map-marker-alt ms-2"></i>محليات</a
                  >
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a class="dropdown-item" href="#"
                    ><i class="fas fa-archive ms-2"></i>أرشيف</a
                  >
                </li>
              </ul>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#"
                ><i class="fas fa-video ms-2"></i>فيديو</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#"
                ><i class="fas fa-images ms-2"></i>صور</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#"
                ><i class="fas fa-phone ms-2"></i>اتصل بنا</a
              >
            </li>
          </ul>

          <form class="d-flex">
            <input
              class="form-control me-2"
              type="search"
              placeholder="البحث في الأخبار..."
            />
            <button class="btn btn-outline-primary" type="submit">
              <i class="fas fa-search"></i>
            </button>
          </form>
        </div>
      </div>
    </nav>

    <!-- ===== المحتوى الرئيسي ===== -->
    <main class="container my-4">
      <div class="row">
        <!-- ===== العمود الرئيسي ===== -->
        <div class="col-lg-9">
          <!-- ===== السلايدر الرئيسي ===== -->
          <section class="hero-slider">
            <div
              id="newsCarousel"
              class="carousel slide"
              data-bs-ride="carousel"
            >
              <div class="carousel-indicators">
                <button
                  type="button"
                  data-bs-target="#newsCarousel"
                  data-bs-slide-to="0"
                  class="active"
                ></button>
                <button
                  type="button"
                  data-bs-target="#newsCarousel"
                  data-bs-slide-to="1"
                ></button>
                <button
                  type="button"
                  data-bs-target="#newsCarousel"
                  data-bs-slide-to="2"
                ></button>
              </div>

              <div class="carousel-inner">
                <div class="carousel-item active">
                  <img
                    src="https://via.placeholder.com/800x400/1e3a8a/ffffff?text=خبر+رئيسي+1"
                    alt="خبر رئيسي"
                  />
                  <div class="carousel-caption">
                    <h5>
                      رئيس الوزراء يعلن عن مشاريع تنموية جديدة في المحافظات
                    </h5>
                    <p>
                      أعلن رئيس مجلس الوزراء عن إطلاق حزمة من المشاريع التنموية
                      الاستراتيجية التي تهدف إلى تحسين الخدمات المقدمة للمواطنين
                      في جميع المحافظات العراقية.
                    </p>
                    <a href="#" class="btn btn-primary">اقرأ المزيد</a>
                  </div>
                </div>

                <div class="carousel-item">
                  <img
                    src="https://via.placeholder.com/800x400/3b82f6/ffffff?text=خبر+رئيسي+2"
                    alt="خبر رئيسي"
                  />
                  <div class="carousel-caption">
                    <h5>وزير التربية يفتتح مدارس جديدة في بغداد والمحافظات</h5>
                    <p>
                      افتتح وزير التربية عدداً من المدارس الحديثة المجهزة بأحدث
                      التقنيات التعليمية، في إطار خطة الوزارة لتطوير القطاع
                      التعليمي.
                    </p>
                    <a href="#" class="btn btn-primary">اقرأ المزيد</a>
                  </div>
                </div>

                <div class="carousel-item">
                  <img
                    src="https://via.placeholder.com/800x400/f59e0b/ffffff?text=خبر+رئيسي+3"
                    alt="خبر رئيسي"
                  />
                  <div class="carousel-caption">
                    <h5>وزارة الصحة تعلن عن حملة تطعيم شاملة</h5>
                    <p>
                      أطلقت وزارة الصحة حملة تطعيم واسعة النطاق تستهدف جميع
                      الفئات العمرية، بهدف الوقاية من الأمراض المعدية وتعزيز
                      الصحة العامة.
                    </p>
                    <a href="#" class="btn btn-primary">اقرأ المزيد</a>
                  </div>
                </div>
              </div>

              <button
                class="carousel-control-prev"
                type="button"
                data-bs-target="#newsCarousel"
                data-bs-slide="prev"
              >
                <span class="carousel-control-prev-icon"></span>
              </button>
              <button
                class="carousel-control-next"
                type="button"
                data-bs-target="#newsCarousel"
                data-bs-slide="next"
              >
                <span class="carousel-control-next-icon"></span>
              </button>
            </div>
          </section>

          <!-- ===== شبكة الأخبار ===== -->
          <section class="news-grid">
            <div class="row">
              <div class="col-12 mb-4">
                <h2 class="section-title">
                  <i class="fas fa-newspaper text-primary ms-2"></i>
                  آخر الأخبار
                </h2>
              </div>
            </div>

            <div class="row">
              <!-- بطاقة خبر 1 -->
              <div class="col-lg-6 col-md-6 col-sm-12">
                <article class="news-card">
                  <img
                    src="https://via.placeholder.com/400x200/1e3a8a/ffffff?text=صورة+الخبر"
                    alt="صورة الخبر"
                  />
                  <div class="news-card-body">
                    <span class="news-category">سياسة</span>
                    <h3 class="news-title">
                      <a href="#" class="text-decoration-none"
                        >اجتماع مجلس الوزراء يناقش الموازنة العامة للدولة</a
                      >
                    </h3>
                    <p class="news-excerpt">
                      عقد مجلس الوزراء جلسته الاعتيادية برئاسة رئيس الوزراء،
                      وناقش عدداً من الملفات المهمة المتعلقة بالموازنة العامة
                      والمشاريع التنموية.
                    </p>
                    <div class="news-meta">
                      <span
                        ><i class="fas fa-calendar ms-1"></i>منذ ساعتين</span
                      >
                      <span><i class="fas fa-eye ms-1"></i>1,234 مشاهدة</span>
                    </div>
                  </div>
                </article>
              </div>

              <!-- بطاقة خبر 2 -->
              <div class="col-lg-6 col-md-6 col-sm-12">
                <article class="news-card">
                  <img
                    src="https://via.placeholder.com/400x200/3b82f6/ffffff?text=صورة+الخبر"
                    alt="صورة الخبر"
                  />
                  <div class="news-card-body">
                    <span class="news-category">اقتصاد</span>
                    <h3 class="news-title">
                      <a href="#" class="text-decoration-none"
                        >ارتفاع أسعار النفط يعزز الإيرادات الحكومية</a
                      >
                    </h3>
                    <p class="news-excerpt">
                      شهدت أسعار النفط العراقي ارتفاعاً ملحوظاً في الأسواق
                      العالمية، مما يساهم في زيادة الإيرادات الحكومية ودعم
                      الموازنة العامة.
                    </p>
                    <div class="news-meta">
                      <span
                        ><i class="fas fa-calendar ms-1"></i>منذ 3 ساعات</span
                      >
                      <span><i class="fas fa-eye ms-1"></i>987 مشاهدة</span>
                    </div>
                  </div>
                </article>
              </div>

              <!-- بطاقة خبر 3 -->
              <div class="col-lg-6 col-md-6 col-sm-12">
                <article class="news-card">
                  <img
                    src="https://via.placeholder.com/400x200/f59e0b/ffffff?text=صورة+الخبر"
                    alt="صورة الخبر"
                  />
                  <div class="news-card-body">
                    <span class="news-category">رياضة</span>
                    <h3 class="news-title">
                      <a href="#" class="text-decoration-none"
                        >المنتخب العراقي يستعد لمباراة مهمة في التصفيات</a
                      >
                    </h3>
                    <p class="news-excerpt">
                      يواصل المنتخب الوطني العراقي تحضيراته للمباراة المهمة
                      القادمة ضمن تصفيات كأس العالم، حيث يسعى لتحقيق نتيجة
                      إيجابية.
                    </p>
                    <div class="news-meta">
                      <span
                        ><i class="fas fa-calendar ms-1"></i>منذ 4 ساعات</span
                      >
                      <span><i class="fas fa-eye ms-1"></i>2,156 مشاهدة</span>
                    </div>
                  </div>
                </article>
              </div>

              <!-- بطاقة خبر 4 -->
              <div class="col-lg-6 col-md-6 col-sm-12">
                <article class="news-card">
                  <img
                    src="https://via.placeholder.com/400x200/10b981/ffffff?text=صورة+الخبر"
                    alt="صورة الخبر"
                  />
                  <div class="news-card-body">
                    <span class="news-category">ثقافة</span>
                    <h3 class="news-title">
                      <a href="#" class="text-decoration-none"
                        >افتتاح مهرجان بغداد الثقافي بمشاركة عربية واسعة</a
                      >
                    </h3>
                    <p class="news-excerpt">
                      انطلقت فعاليات مهرجان بغداد الثقافي السنوي بمشاركة واسعة
                      من المثقفين والأدباء من مختلف البلدان العربية والعالمية.
                    </p>
                    <div class="news-meta">
                      <span
                        ><i class="fas fa-calendar ms-1"></i>منذ 5 ساعات</span
                      >
                      <span><i class="fas fa-eye ms-1"></i>756 مشاهدة</span>
                    </div>
                  </div>
                </article>
              </div>
            </div>
          </section>
        </div>

        <!-- ===== الشريط الجانبي ===== -->
        <div class="col-lg-3">
          <!-- الأخبار الأكثر قراءة -->
          <aside class="sidebar">
            <h4 class="sidebar-title">
              <i class="fas fa-fire text-danger ms-2"></i>
              الأكثر قراءة
            </h4>

            <div class="trending-item">
              <div class="trending-number">1</div>
              <div class="trending-content">
                <h6>
                  <a href="#" class="text-decoration-none"
                    >رئيس الوزراء يبحث مع الوفد الأمريكي العلاقات الثنائية</a
                  >
                </h6>
                <small><i class="fas fa-clock ms-1"></i>منذ ساعة واحدة</small>
              </div>
            </div>

            <div class="trending-item">
              <div class="trending-number">2</div>
              <div class="trending-content">
                <h6>
                  <a href="#" class="text-decoration-none"
                    >وزارة النفط توقع اتفاقية جديدة لتطوير الحقول النفطية</a
                  >
                </h6>
                <small><i class="fas fa-clock ms-1"></i>منذ ساعتين</small>
              </div>
            </div>

            <div class="trending-item">
              <div class="trending-number">3</div>
              <div class="trending-content">
                <h6>
                  <a href="#" class="text-decoration-none"
                    >البرلمان يصوت على قانون الموازنة العامة للدولة</a
                  >
                </h6>
                <small><i class="fas fa-clock ms-1"></i>منذ 3 ساعات</small>
              </div>
            </div>

            <div class="trending-item">
              <div class="trending-number">4</div>
              <div class="trending-content">
                <h6>
                  <a href="#" class="text-decoration-none"
                    >وزير الداخلية يعلن عن خطة أمنية شاملة للمحافظات</a
                  >
                </h6>
                <small><i class="fas fa-clock ms-1"></i>منذ 4 ساعات</small>
              </div>
            </div>

            <div class="trending-item">
              <div class="trending-number">5</div>
              <div class="trending-content">
                <h6>
                  <a href="#" class="text-decoration-none"
                    >افتتاح مشروع إسكان جديد في بغداد يضم 1000 وحدة سكنية</a
                  >
                </h6>
                <small><i class="fas fa-clock ms-1"></i>منذ 5 ساعات</small>
              </div>
            </div>
          </aside>

          <!-- روابط سريعة -->
          <aside class="sidebar">
            <h4 class="sidebar-title">
              <i class="fas fa-link text-primary ms-2"></i>
              روابط مهمة
            </h4>

            <div class="list-group list-group-flush">
              <a
                href="#"
                class="list-group-item list-group-item-action d-flex align-items-center"
              >
                <i class="fas fa-external-link-alt text-primary ms-2"></i>
                رئاسة الجمهورية
              </a>
              <a
                href="#"
                class="list-group-item list-group-item-action d-flex align-items-center"
              >
                <i class="fas fa-external-link-alt text-primary ms-2"></i>
                رئاسة مجلس الوزراء
              </a>
              <a
                href="#"
                class="list-group-item list-group-item-action d-flex align-items-center"
              >
                <i class="fas fa-external-link-alt text-primary ms-2"></i>
                مجلس النواب العراقي
              </a>
              <a
                href="#"
                class="list-group-item list-group-item-action d-flex align-items-center"
              >
                <i class="fas fa-external-link-alt text-primary ms-2"></i>
                البنك المركزي العراقي
              </a>
              <a
                href="#"
                class="list-group-item list-group-item-action d-flex align-items-center"
              >
                <i class="fas fa-external-link-alt text-primary ms-2"></i>
                وزارة الخارجية
              </a>
            </div>
          </aside>

          <!-- الطقس -->
          <aside class="sidebar">
            <h4 class="sidebar-title">
              <i class="fas fa-cloud-sun text-warning ms-2"></i>
              حالة الطقس - بغداد
            </h4>

            <div class="text-center">
              <div class="weather-icon mb-3">
                <i class="fas fa-sun text-warning" style="font-size: 3rem"></i>
              </div>
              <h3 class="text-primary mb-2">32°C</h3>
              <p class="mb-2">مشمس جزئياً</p>
              <div class="row text-center">
                <div class="col-6">
                  <small class="text-muted">العظمى</small>
                  <div class="fw-bold">35°</div>
                </div>
                <div class="col-6">
                  <small class="text-muted">الصغرى</small>
                  <div class="fw-bold">28°</div>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </div>
    </main>

    <!-- ===== الفوتر ===== -->
    <footer class="main-footer">
      <div class="container">
        <div class="row">
          <div class="col-lg-4 col-md-6 mb-4">
            <div class="footer-section">
              <h5>عن الموقع</h5>
              <p class="text-light">
                بوابة الأخبار الحكومية الرسمية لجمهورية العراق، نقدم لكم آخر
                الأخبار والتطورات الحكومية والسياسية والاقتصادية بمصداقية عالية
                وشفافية تامة.
              </p>
              <div class="social-links">
                <a href="#" class="text-light me-3"
                  ><i class="fab fa-facebook-f"></i
                ></a>
                <a href="#" class="text-light me-3"
                  ><i class="fab fa-twitter"></i
                ></a>
                <a href="#" class="text-light me-3"
                  ><i class="fab fa-youtube"></i
                ></a>
                <a href="#" class="text-light me-3"
                  ><i class="fab fa-telegram"></i
                ></a>
              </div>
            </div>
          </div>

          <div class="col-lg-2 col-md-6 mb-4">
            <div class="footer-section">
              <h5>الأقسام</h5>
              <ul class="footer-links">
                <li><a href="#">سياسة</a></li>
                <li><a href="#">اقتصاد</a></li>
                <li><a href="#">رياضة</a></li>
                <li><a href="#">ثقافة</a></li>
                <li><a href="#">محليات</a></li>
                <li><a href="#">أرشيف</a></li>
              </ul>
            </div>
          </div>

          <div class="col-lg-3 col-md-6 mb-4">
            <div class="footer-section">
              <h5>روابط مهمة</h5>
              <ul class="footer-links">
                <li><a href="#">اتصل بنا</a></li>
                <li><a href="#">سياسة الخصوصية</a></li>
                <li><a href="#">شروط الاستخدام</a></li>
                <li><a href="#">خريطة الموقع</a></li>
                <li><a href="#">الأسئلة الشائعة</a></li>
                <li><a href="#">تقديم شكوى</a></li>
              </ul>
            </div>
          </div>

          <div class="col-lg-3 col-md-6 mb-4">
            <div class="footer-section">
              <h5>معلومات التواصل</h5>
              <div class="contact-info">
                <p>
                  <i class="fas fa-map-marker-alt ms-2"></i>بغداد - المنطقة
                  الخضراء
                </p>
                <p><i class="fas fa-phone ms-2"></i>+964 1 123 4567</p>
                <p><i class="fas fa-envelope ms-2"></i><EMAIL></p>
                <p><i class="fas fa-fax ms-2"></i>+964 1 123 4568</p>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-bottom">
          <div class="row align-items-center">
            <div class="col-md-6">
              <p class="mb-0">
                © 2024 بوابة الأخبار الحكومية - جمهورية العراق. جميع الحقوق
                محفوظة.
              </p>
            </div>
            <div class="col-md-6 text-end">
              <img
                src="https://via.placeholder.com/100x50/ffffff/1e3a8a?text=شعار+الوزارة"
                alt="شعار وزارة الإعلام"
                class="footer-logo"
              />
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- ===== مكتبات JavaScript ===== -->
    <!-- Bootstrap 5.3 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <!-- كود JavaScript المخصص -->
    <script>
      $(document).ready(function () {
        // ===== تفعيل الوضع الداكن =====
        const themeToggle = $('#themeToggle');
        const themeIcon = $('#themeIcon');
        const themeText = $('#themeText');
        const body = $('body');

        // التحقق من الوضع المحفوظ في localStorage
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
          body.attr('data-theme', savedTheme);
          updateThemeButton(savedTheme);
        }

        // تبديل الوضع عند النقر على الزر
        themeToggle.on('click', function () {
          const currentTheme = body.attr('data-theme');
          const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

          body.attr('data-theme', newTheme);
          localStorage.setItem('theme', newTheme);
          updateThemeButton(newTheme);
        });

        // تحديث نص وأيقونة زر الوضع
        function updateThemeButton(theme) {
          if (theme === 'dark') {
            themeIcon.removeClass('fa-moon').addClass('fa-sun');
            themeText.text('الوضع الفاتح');
          } else {
            themeIcon.removeClass('fa-sun').addClass('fa-moon');
            themeText.text('الوضع الداكن');
          }
        }

        // ===== تفعيل السلايدر التلقائي =====
        const carousel = $('#newsCarousel');

        // إيقاف السلايدر عند التمرير فوقه
        carousel.on('mouseenter', function () {
          $(this).carousel('pause');
        });

        // استئناف السلايدر عند مغادرة المؤشر
        carousel.on('mouseleave', function () {
          $(this).carousel('cycle');
        });

        // ===== تأثيرات التمرير =====
        $(window).on('scroll', function () {
          const scrollTop = $(this).scrollTop();

          // إضافة ظل للنافبار عند التمرير
          if (scrollTop > 100) {
            $('.main-navbar').addClass('shadow-lg');
          } else {
            $('.main-navbar').removeClass('shadow-lg');
          }
        });

        // ===== تأثيرات hover المحسنة للبطاقات =====
        $('.news-card')
          .on('mouseenter', function () {
            $(this).find('img').css('transform', 'scale(1.05)');
            $(this).addClass('card-hover');
          })
          .on('mouseleave', function () {
            $(this).find('img').css('transform', 'scale(1)');
            $(this).removeClass('card-hover');
          });

        // ===== تأثيرات للشريط الجانبي =====
        $('.trending-item')
          .on('mouseenter', function () {
            $(this).find('.trending-number').addClass('number-hover');
          })
          .on('mouseleave', function () {
            $(this).find('.trending-number').removeClass('number-hover');
          });

        // ===== تحسين تجربة القوائم المنسدلة =====
        $('.dropdown-toggle').on('click', function () {
          const dropdown = $(this).next('.dropdown-menu');
          dropdown.addClass('dropdown-show');

          setTimeout(() => {
            dropdown.removeClass('dropdown-show');
          }, 300);
        });

        // ===== تأثير النقر على الروابط =====
        $('a[href="#"]').on('click', function (e) {
          e.preventDefault();

          // إضافة تأثير بصري للنقر
          $(this).addClass('clicked');
          setTimeout(() => {
            $(this).removeClass('clicked');
          }, 200);

          // يمكن إضافة منطق التنقل هنا
          console.log('تم النقر على الرابط: ' + $(this).text());
        });

        // ===== تحسين تجربة البحث =====
        const searchInput = $('input[type="search"]');

        searchInput
          .on('focus', function () {
            $(this).parent().addClass('search-focused');
          })
          .on('blur', function () {
            $(this).parent().removeClass('search-focused');
          });

        // ===== تأثيرات الأزرار =====
        $('.btn')
          .on('mousedown', function () {
            $(this).addClass('btn-pressed');
          })
          .on('mouseup mouseleave', function () {
            $(this).removeClass('btn-pressed');
          });

        // ===== تحديث الوقت بشكل ديناميكي =====
        function updateTime() {
          const now = new Date();
          const timeString = now.toLocaleTimeString('ar-IQ', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
          });

          // يمكن إضافة عنصر لعرض الوقت إذا لزم الأمر
          // $('#current-time').text(timeString);
        }

        // تحديث الوقت كل ثانية
        setInterval(updateTime, 1000);

        // ===== تحسين الاستجابة للشاشات الصغيرة =====
        function handleResponsive() {
          const windowWidth = $(window).width();

          if (windowWidth < 768) {
            // تعديلات للشاشات الصغيرة
            $('.news-card').removeClass('col-lg-6').addClass('col-12');
          } else {
            // إعادة التعديلات للشاشات الكبيرة
            $('.news-card').removeClass('col-12').addClass('col-lg-6');
          }
        }

        // تشغيل عند تحميل الصفحة وتغيير حجم النافذة
        handleResponsive();
        $(window).on('resize', handleResponsive);

        // ===== رسائل التأكيد للتفاعل =====
        $('.social-links a').on('click', function (e) {
          e.preventDefault();
          const platform = $(this)
            .find('i')
            .attr('class')
            .split(' ')[1]
            .replace('fa-', '');

          // يمكن إضافة toast notification هنا
          console.log('تم النقر على رابط ' + platform);
        });

        // ===== تحسين تجربة التنقل =====
        $('.navbar-nav .nav-link').on('click', function () {
          // إزالة الفئة النشطة من جميع الروابط
          $('.navbar-nav .nav-link').removeClass('active');
          // إضافة الفئة النشطة للرابط المنقور
          $(this).addClass('active');
        });

        // ===== تأثيرات التحميل =====
        $('.news-card img').on('load', function () {
          $(this).addClass('loaded');
        });

        // ===== إضافة CSS للتأثيرات الديناميكية المحسنة =====
        $('<style>')
          .prop('type', 'text/css')
          .html(
            `
                    .clicked {
                        transform: scale(0.95);
                        transition: transform 0.1s ease;
                    }

                    .search-focused {
                        transform: scale(1.02);
                        transition: transform 0.2s ease;
                    }

                    .btn-pressed {
                        transform: scale(0.98);
                        transition: transform 0.1s ease;
                    }

                    .news-card img {
                        transition: transform 0.4s ease;
                        opacity: 0;
                    }

                    .news-card img.loaded {
                        opacity: 1;
                        animation: fadeInUp 0.6s ease;
                    }

                    .main-navbar {
                        transition: box-shadow 0.3s ease;
                    }

                    .card-hover {
                        animation: cardPulse 0.3s ease;
                    }

                    .number-hover {
                        animation: numberBounce 0.4s ease;
                    }

                    .dropdown-show {
                        animation: dropdownSlide 0.3s ease;
                    }

                    @keyframes fadeInUp {
                        from {
                            opacity: 0;
                            transform: translateY(20px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }

                    @keyframes cardPulse {
                        0% { transform: scale(1); }
                        50% { transform: scale(1.02); }
                        100% { transform: scale(1.02); }
                    }

                    @keyframes numberBounce {
                        0%, 100% { transform: scale(1); }
                        50% { transform: scale(1.1); }
                    }

                    @keyframes dropdownSlide {
                        from {
                            opacity: 0;
                            transform: translateY(-10px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }

                    .news-card {
                        animation: slideInUp 0.6s ease;
                    }

                    .news-card:nth-child(1) { animation-delay: 0.1s; }
                    .news-card:nth-child(2) { animation-delay: 0.2s; }
                    .news-card:nth-child(3) { animation-delay: 0.3s; }
                    .news-card:nth-child(4) { animation-delay: 0.4s; }

                    @keyframes slideInUp {
                        from {
                            opacity: 0;
                            transform: translateY(30px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }

                    .sidebar {
                        animation: slideInRight 0.6s ease;
                        animation-delay: 0.3s;
                        animation-fill-mode: both;
                    }

                    @keyframes slideInRight {
                        from {
                            opacity: 0;
                            transform: translateX(30px);
                        }
                        to {
                            opacity: 1;
                            transform: translateX(0);
                        }
                    }
                `
          )
          .appendTo('head');
      });
    </script>
  </body>
</html>
